import { Injectable } from '@nestjs/common';
import {
  TargetEntity,
  TargetRepository,
  TargetTypeEntity,
  TargetTypeRepository,
} from '@ghq-abi/northstar-domain';
import { CreateTargetDto, TargetOrderItem } from './dtos';

@Injectable()
export class TargetsService {
  constructor(
    private readonly targetRepository: TargetRepository,
    private readonly targetTypeRepository: TargetTypeRepository,
  ) {}

  async createTarget(target: CreateTargetDto): Promise<TargetEntity> {
    const createdTarget = await this.targetRepository.createTarget({
      scope: target.scope,
      weight: target.weight,
      uidProposal: target.uidProposal,
    } as unknown as TargetEntity);

    if (target.children && target.children.length > 0) {
      await this.targetRepository.createTargets(
        target.children.map(
          (child) =>
            ({
              ...child,
              uidParentTarget: createdTarget.uid,
            }) as unknown as TargetEntity,
        ),
      );
    }

    if (target.targetType) {
      await this.targetTypeRepository.create({
        uidTarget: createdTarget.uid,
        type: target.targetType,
      } as unknown as TargetTypeEntity);
    }

    return createdTarget;
  }

  async createTargets(targets: TargetEntity[]): Promise<TargetEntity[]> {
    return this.targetRepository.createTargets(targets);
  }

  async findAllTargets(): Promise<TargetEntity[]> {
    return this.targetRepository.findAllTargets();
  }

  async findTargetByUid(uid: string): Promise<TargetEntity | null> {
    return this.targetRepository.findByUid(uid);
  }

  async deleteTargetsByUids(uids: string[]): Promise<void> {
    await this.targetRepository.deleteTargetsByUids(uids);
  }

  async updateTargetOrdering(targets: TargetOrderItem[]): Promise<void> {
    await this.targetRepository.updateTargetOrdering(targets);
  }
}
