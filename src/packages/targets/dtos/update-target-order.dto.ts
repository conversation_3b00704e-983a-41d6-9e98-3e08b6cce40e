import { ApiProperty } from '@nestjs/swagger';
import {
  IsArray,
  IsNotEmpty,
  IsNumber,
  IsUUID,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';

export class TargetOrderItem {
  @ApiProperty({
    description: 'Unique identifier for the target',
    example: 'A70BB61B-47DF-4A4B-9525-36118C76AD8A',
  })
  @IsUUID()
  @IsNotEmpty()
  uid: string;

  @ApiProperty({
    description: 'New order position for the target',
    example: 1,
  })
  @IsNumber()
  @IsNotEmpty()
  order: number;
}

export class UpdateTargetOrderDto {
  @ApiProperty({
    description: 'Array of targets with their new order positions',
    type: [TargetOrderItem],
    example: [
      {
        uid: 'A70BB61B-47DF-4A4B-9525-36118C76AD8A',
        order: 1,
      },
      {
        uid: 'B80CC62C-58EE-5B5C-C636-47229D87BE9B',
        order: 2,
      },
    ],
  })
  @IsArray()
  @IsNotEmpty()
  @ValidateNested({ each: true })
  @Type(() => TargetOrderItem)
  targets: TargetOrderItem[];
}
