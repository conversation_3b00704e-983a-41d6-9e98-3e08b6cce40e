import {
  Body,
  Controller,
  Get,
  Patch,
  Post,
  BadRequestException,
} from '@nestjs/common';
import { TargetsService } from './targets.service';
import { ApiBody, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { CreateTargetDto, TargetOrderItem } from './dtos';

@ApiTags('Targets')
@Controller('targets')
export class TargetsController {
  constructor(private readonly targetsService: TargetsService) {}

  @Get()
  public async getTargets() {
    return this.targetsService.findAllTargets();
  }

  @Post()
  public async createTarget(@Body() target: CreateTargetDto) {
    return this.targetsService.createTarget(target);
  }

  @Patch('order-list')
  @ApiOperation({
    summary: 'Update target ordering',
    description:
      'Updates the order of targets by providing an array of target UIDs with their new order positions',
  })
  @ApiBody({
    type: [TargetOrderItem],
    description: 'Array of targets with their new order positions',
  })
  @ApiResponse({
    status: 200,
    description: 'Target ordering updated successfully',
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid target UIDs or order values',
  })
  public async updateTargetOrdering(
    @Body() targets: TargetOrderItem[],
  ): Promise<void> {
    // Manual validation for array
    if (!Array.isArray(targets)) {
      throw new BadRequestException('Request body must be an array');
    }

    if (targets.length === 0) {
      throw new BadRequestException('Array cannot be empty');
    }

    // Validate each item
    for (const target of targets) {
      if (!target.uid || typeof target.uid !== 'string') {
        throw new BadRequestException('Each target must have a valid uid');
      }

      // Basic UUID format validation
      const uuidRegex =
        /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
      if (!uuidRegex.test(target.uid)) {
        throw new BadRequestException('Each target uid must be a valid UUID');
      }

      if (typeof target.order !== 'number') {
        throw new BadRequestException(
          'Each target must have a valid order number',
        );
      }
    }

    return this.targetsService.updateTargetOrdering(targets);
  }
}
