import { Body, Controller, Get, Patch, Post } from '@nestjs/common';
import { TargetsService } from './targets.service';
import { ApiBody, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { CreateTargetDto, UpdateTargetOrderDto } from './dtos';

@ApiTags('Targets')
@Controller('targets')
export class TargetsController {
  constructor(private readonly targetsService: TargetsService) {}

  @Get()
  public async getTargets() {
    return this.targetsService.findAllTargets();
  }

  @Post()
  public async createTarget(@Body() target: CreateTargetDto) {
    return this.targetsService.createTarget(target);
  }

  @Patch('order-list')
  @ApiOperation({
    summary: 'Update target ordering',
    description:
      'Updates the order of targets by providing an array of target UIDs with their new order positions',
  })
  @ApiBody({
    type: UpdateTargetOrderDto,
    description: 'Array of targets with their new order positions',
  })
  @ApiResponse({
    status: 200,
    description: 'Target ordering updated successfully',
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid target UIDs or order values',
  })
  public async updateTargetOrdering(
    @Body() updateTargetOrderDto: UpdateTargetOrderDto,
  ): Promise<void> {
    return this.targetsService.updateTargetOrdering(updateTargetOrderDto);
  }
}
